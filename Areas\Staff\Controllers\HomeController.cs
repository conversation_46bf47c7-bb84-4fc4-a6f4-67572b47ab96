using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Identity;
using MyMvcApp.Models;
using Microsoft.EntityFrameworkCore;
using MyMvcApp.Data;

namespace MyMvcApp.Areas.Staff.Controllers
{
    [Area("Staff")]
    [Authorize(Roles = "Admin,Staff")]
    public class HomeController : Controller
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly ApplicationDbContext _context;

        public HomeController(
            UserManager<ApplicationUser> userManager,
            ApplicationDbContext context)
        {
            _userManager = userManager;
            _context = context;
        }

        public async Task<IActionResult> Index()
        {
            // Get dashboard statistics for Staff
            ViewBag.TotalPatients = await _context.Patients.CountAsync();
            ViewBag.TodayAppointments = await _context.Appointments
                .CountAsync(a => a.AppointmentDate.Date == DateTime.Today);
            ViewBag.TotalServices = await _context.Services.CountAsync();
            ViewBag.PendingPayments = await _context.PaymentTransactions
                .CountAsync(p => p.Status == "Pending");

            // Get recent activities
            var recentActivities = await _context.Activities
                .Include(a => a.User)
                .OrderByDescending(a => a.Time)
                .Take(10)
                .Select(a => new
                {
                    Time = a.Time,
                    Description = a.Description,
                    User = a.User.FullName
                })
                .ToListAsync();

            ViewBag.RecentActivities = recentActivities;

            return View();
        }
    }
}
